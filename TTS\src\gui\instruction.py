"""
说明界面模块，用于显示软件使用说明
"""
import tkinter as tk
from tkinter import ttk
import os

class InstructionFrame(ttk.Frame):
    """说明界面，显示软件使用说明"""

    def __init__(self, parent):
        """
        初始化说明界面
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建界面组件
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, padx=10, pady=(20, 5))
        
        ttk.Label(title_frame, text="使用说明", 
                 font=("微软雅黑", 16, "bold")).pack(anchor=tk.CENTER)
        
        # 分隔线
        ttk.Separator(self, orient="horizontal").pack(fill=tk.X, padx=20, pady=10)
        
        # 说明文本区域
        text_frame = ttk.Frame(self)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建文本框和滚动条
        self.instruction_text = tk.Text(text_frame, 
                                       font=("微软雅黑", 10), 
                                       wrap=tk.WORD, 
                                       bg="#f9f9f9", 
                                       padx=10, 
                                       pady=10)
        scrollbar = ttk.Scrollbar(text_frame, command=self.instruction_text.yview)
        self.instruction_text.configure(yscrollcommand=scrollbar.set)
        
        # 放置组件
        self.instruction_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置文本为只读
        self.instruction_text.configure(state="normal")
        
        # 加载说明文本
        self.load_instruction()
        
        # 设置文本为只读
        self.instruction_text.configure(state="disabled")
        
    def load_instruction(self):
        """加载说明文本"""
        instruction_text = """
# 暂无说明
"""
        
        # 插入说明文本
        self.instruction_text.delete("1.0", tk.END)
        self.instruction_text.insert("1.0", instruction_text)
        
    def apply_text_styles(self):
        """应用文本样式，设置标题和重点文本的样式"""
        # 创建标签
        self.instruction_text.tag_configure("title1", font=("微软雅黑", 14, "bold"), foreground="#2196F3")
        self.instruction_text.tag_configure("title2", font=("微软雅黑", 12, "bold"), foreground="#0D47A1")
        self.instruction_text.tag_configure("title3", font=("微软雅黑", 11, "bold"), foreground="#1976D2")
        self.instruction_text.tag_configure("bold", font=("微软雅黑", 10, "bold"))
        
        # 应用样式
        lines = self.instruction_text.get("1.0", tk.END).split("\n")
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # 处理大标题 (# 开头)
            if line.startswith("# "):
                self.instruction_text.tag_add("title1", f"{line_num}.0", f"{line_num}.end")
            
            # 处理二级标题 (## 开头)
            elif line.startswith("## "):
                self.instruction_text.tag_add("title2", f"{line_num}.0", f"{line_num}.end")
            
            # 处理三级标题 (### 开头)
            elif line.startswith("### "):
                self.instruction_text.tag_add("title3", f"{line_num}.0", f"{line_num}.end")
                
            # 处理加粗文本 (** 包围)
            else:
                # 查找所有 ** 包围的文本
                start = 0
                while True:
                    start = line.find("**", start)
                    if start == -1:
                        break
                    end = line.find("**", start + 2)
                    if end == -1:
                        break
                    
                    # 应用粗体样式
                    self.instruction_text.tag_add("bold", f"{line_num}.{start}", f"{line_num}.{end+2}")
                    
                    # 更新起始位置
                    start = end + 2 